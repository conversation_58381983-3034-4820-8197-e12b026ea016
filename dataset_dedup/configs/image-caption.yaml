# 针对我们的Image-Caption数据类别的数据集选择的去重方法流程

data_source:
  name: "json_list"
  path: "test_data/sample_eiffel_data.json"

output_path: "output/cleaned_data.json"

fingerprint_target:
  image: "images[0]"

pipeline:
  # stage1
  - stage: "1. 图片文件内容完全相同情况的去重"
    fingerprinter:
      name: "file_content_hash"
    comparer:
      name: "hash_map"
    strategy:
      name: "keep_first"

  # stage2
  - stage: "2. 图片近似的去重,如调整过对比度、色调的同一张图"
    fingerprinter:
      name: "phash"
    comparer:
      name: "hamming_distance"
      params:
        threshold: 4
    strategy:
      name: "keep_first"

    # Stage3
  - stage: "3. 快手方法：pHash+minHash+Jaccard相似度去重（识别复杂变化如部分裁剪）"
    fingerprinter:
      name: "phash_minhash"
      params:
        num_perm: 128  # 128位排列哈希
    comparer:
      name: "jaccard_similarity"
      params:
        threshold: 0.95  # Jaccard相似度阈值>0.95
    strategy:
      name: "keep_first"

  # stage4
  - stage: "4. 图片语义近似的去重"
    fingerprinter:
      name: "image_embedder"
      params:
        model_name: "openai/clip-vit-base-patch32"
    comparer:
      name: "faiss_index"
      params:
        threshold: 0.95
    strategy:
        name: "keep_first"