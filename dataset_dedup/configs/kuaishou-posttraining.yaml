# 快手论文后训练阶段去重方法配置
# 基于双重CLIP编码器的图像(0.98) OR 文本(0.50)阈值去重

data_source:
  name: "json_list"
  path: "test_data/sample_eiffel_data.json"

output_path: "output/kuaishou_posttraining_cleaned.json"

fingerprint_target:
  image: "images[0]"
  text: "messages[1].content"  # assistant的回复内容

pipeline:
  # Stage 1: 快手后训练方法 - 双重CLIP编码器 + 双重阈值
  - stage: "1. 快手后训练方法：双重CLIP编码器+双重阈值去重"
    fingerprinter:
      name: "dual_clip"
      params:
        model_name: "openai/clip-vit-base-patch32"
    comparer:
      name: "dual_threshold"
      params:
        image_threshold: 0.98  # 图像相似度阈值
        text_threshold: 0.50   # 文本相似度阈值
    strategy:
      name: "keep_first"
