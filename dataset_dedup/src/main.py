import argparse
import yaml
import sys
from .pipeline_executor import PipelineExecutor

def main():
    parser = argparse.ArgumentParser(description="A flexible framework for dataset deduplication.")
    parser.add_argument('--config', type=str, required=True, help="Path to the configuration YAML file.")
    args = parser.parse_args()

    try:
        print(f"Loading configuration from {args.config}")
        with open(args.config, 'r') as f:
            config = yaml.safe_load(f)

        executor = PipelineExecutor(config)
        executor.run()

    except Exception as e:
        print(f"\nERROR: An error occurred during the pipeline execution.")
        print(f"Details: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()