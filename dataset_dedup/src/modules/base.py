from abc import ABC, abstractmethod
from typing import List, Dict, Any
from ..core.data_model import DataUnit, Fingerprint, DuplicateCluster

class BaseLoader(ABC):
    @abstractmethod
    def load(self, path: str) -> List[DataUnit]:
        pass

class BaseFingerprinter(ABC):
    @abstractmethod
    def fingerprint(self, data_unit: DataUnit, **kwargs) -> Fingerprint:
        pass

class BaseComparer(ABC):
    @abstractmethod
    def find_duplicates(self, fingerprints: List[Fingerprint]) -> List[DuplicateCluster]:
        pass

class BaseStrategy(ABC):
    @abstractmethod
    def apply(self, clusters: List[DuplicateCluster], id_to_data_unit: Dict[str, DataUnit], current_survivor_list: List[str]) -> List[str]:
        pass

class BaseExporter(ABC):
    @abstractmethod
    def export(self, survivor_ids: List[str], output_path: str, id_to_data_unit: Dict[str, DataUnit]):
        pass