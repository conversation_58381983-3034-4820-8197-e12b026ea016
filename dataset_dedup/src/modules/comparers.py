import numpy as np
import faiss
from typing import List, Dict
from tqdm import tqdm
from ..core.data_model import Fingerprint, DuplicateCluster
from .base import BaseComparer

class HashMapComparer(BaseComparer):
    def find_duplicates(self, fingerprints: List[Fingerprint]) -> List[DuplicateCluster]:
        value_to_ids: Dict[str, List[str]] = {}
        for fp in fingerprints:
            if fp.value and isinstance(fp.value, str):
                if fp.value not in value_to_ids: value_to_ids[fp.value] = []
                value_to_ids[fp.value].append(fp.id)
        return [DuplicateCluster(ids=ids) for ids in value_to_ids.values() if len(ids) > 1]

class HammingDistanceComparer(BaseComparer):
    def __init__(self, threshold: int = 4):
        self.threshold = threshold

    def find_duplicates(self, fingerprints: List[Fingerprint]) -> List[DuplicateCluster]:
        valid_fps = [fp for fp in fingerprints if fp.value and isinstance(fp.value, str)]
        hashes = {fp.id: int(fp.value, 16) for fp in valid_fps if isinstance(fp.value, str)}
        ids = [fp.id for fp in valid_fps]
        
        clusters, clustered_ids = [], set()
        for i in tqdm(range(len(ids)), desc="Comparing Hamming Distances"):
            id1 = ids[i]
            if id1 in clustered_ids: continue
            current_cluster = [id1]
            for j in range(i + 1, len(ids)):
                id2 = ids[j]
                if id2 in clustered_ids: continue
                if bin(hashes[id1] ^ hashes[id2]).count('1') <= self.threshold:
                    current_cluster.append(id2)
            if len(current_cluster) > 1:
                clusters.append(DuplicateCluster(ids=current_cluster))
                clustered_ids.update(current_cluster)
        return clusters

class FAISSComparer(BaseComparer):
    def __init__(self, threshold: float = 0.98, batch_size: int = 256):
        self.threshold = threshold
        self.batch_size = batch_size

    def find_duplicates(self, fingerprints: List[Fingerprint]) -> List[DuplicateCluster]:
        valid_fps = [fp for fp in fingerprints if fp.value and isinstance(fp.value, list)]
        if not valid_fps: return []
        
        ids = [fp.id for fp in valid_fps]
        vectors = np.array([fp.value for fp in valid_fps], dtype=np.float32)
        dimension = vectors.shape[1]
        
        index = faiss.IndexFlatIP(dimension)
        index.add(x=vectors)
        
        D, I = index.search(vectors, k=min(100, len(vectors)))
        
        clusters, processed_ids = [], set()
        for i in tqdm(range(len(ids)), desc="Processing FAISS results"):
            if ids[i] in processed_ids: continue
            
            neighbors_indices = I[i][D[i] >= self.threshold]
            if len(neighbors_indices) > 1:
                cluster_ids = [ids[j] for j in neighbors_indices]
                clusters.append(DuplicateCluster(ids=cluster_ids))
                processed_ids.update(cluster_ids)
        return clusters

COMPARER_REGISTRY = {
    "hash_map": HashMapComparer,
    "hamming_distance": HammingDistanceComparer,
    "faiss_index": FAISSComparer,
}