import numpy as np
import faiss
from typing import List, Dict
from tqdm import tqdm
from ..core.data_model import Fingerprint, DuplicateCluster
from .base import BaseComparer

class HashMapComparer(BaseComparer):
    def find_duplicates(self, fingerprints: List[Fingerprint]) -> List[DuplicateCluster]:
        value_to_ids: Dict[str, List[str]] = {}
        for fp in fingerprints:
            if fp.value and isinstance(fp.value, str):
                if fp.value not in value_to_ids: value_to_ids[fp.value] = []
                value_to_ids[fp.value].append(fp.id)
        return [DuplicateCluster(ids=ids) for ids in value_to_ids.values() if len(ids) > 1]

class HammingDistanceComparer(BaseComparer):
    def __init__(self, threshold: int = 4):
        self.threshold = threshold

    def find_duplicates(self, fingerprints: List[Fingerprint]) -> List[DuplicateCluster]:
        valid_fps = [fp for fp in fingerprints if fp.value and isinstance(fp.value, str)]
        hashes = {fp.id: int(fp.value, 16) for fp in valid_fps if isinstance(fp.value, str)}
        ids = [fp.id for fp in valid_fps]
        
        clusters, clustered_ids = [], set()
        for i in tqdm(range(len(ids)), desc="Comparing Hamming Distances"):
            id1 = ids[i]
            if id1 in clustered_ids: continue
            current_cluster = [id1]
            for j in range(i + 1, len(ids)):
                id2 = ids[j]
                if id2 in clustered_ids: continue
                if bin(hashes[id1] ^ hashes[id2]).count('1') <= self.threshold:
                    current_cluster.append(id2)
            if len(current_cluster) > 1:
                clusters.append(DuplicateCluster(ids=current_cluster))
                clustered_ids.update(current_cluster)
        return clusters

class FAISSComparer(BaseComparer):
    def __init__(self, threshold: float = 0.98, batch_size: int = 256):
        self.threshold = threshold
        self.batch_size = batch_size

    def find_duplicates(self, fingerprints: List[Fingerprint]) -> List[DuplicateCluster]:
        valid_fps = [fp for fp in fingerprints if fp.value and isinstance(fp.value, list)]
        if not valid_fps: return []
        
        ids = [fp.id for fp in valid_fps]
        vectors = np.array([fp.value for fp in valid_fps], dtype=np.float32)
        dimension = vectors.shape[1]
        
        index = faiss.IndexFlatIP(dimension)
        index.add(vectors)

        D, I = index.search(vectors, k=min(100, len(vectors)))
        
        clusters, processed_ids = [], set()
        for i in tqdm(range(len(ids)), desc="Processing FAISS results"):
            if ids[i] in processed_ids: continue
            
            neighbors_indices = I[i][D[i] >= self.threshold]
            if len(neighbors_indices) > 1:
                cluster_ids = [ids[j] for j in neighbors_indices]
                clusters.append(DuplicateCluster(ids=cluster_ids))
                processed_ids.update(cluster_ids)
        return clusters

class JaccardSimilarityComparer(BaseComparer):
    """快手论文中的Jaccard相似度比较器"""
    def __init__(self, threshold: float = 0.95):
        self.threshold = threshold

    def find_duplicates(self, fingerprints: List[Fingerprint]) -> List[DuplicateCluster]:
        valid_fps = [fp for fp in fingerprints if fp.value and isinstance(fp.value, list)]
        if not valid_fps:
            return []

        # 构建LSH桶进行候选对生成
        buckets = {}
        for fp in valid_fps:
            # 使用minHash的前几个值作为桶的键
            if fp.value and len(fp.value) >= 5:
                bucket_key = tuple(fp.value[:5])  # 使用前5个minHash值作为桶键
                if bucket_key not in buckets:
                    buckets[bucket_key] = []
                buckets[bucket_key].append(fp)

        clusters = []
        processed_ids = set()

        # 对每个桶中的候选对计算Jaccard相似度
        for bucket_fps in buckets.values():
            if len(bucket_fps) < 2:
                continue

            for i in tqdm(range(len(bucket_fps)), desc="Computing Jaccard similarity"):
                if bucket_fps[i].id in processed_ids:
                    continue

                current_cluster = [bucket_fps[i].id]
                for j in range(i + 1, len(bucket_fps)):
                    if bucket_fps[j].id in processed_ids:
                        continue

                    # 计算Jaccard相似度
                    set1 = set(bucket_fps[i].value)
                    set2 = set(bucket_fps[j].value)
                    jaccard = len(set1.intersection(set2)) / len(set1.union(set2))

                    if jaccard > self.threshold:
                        current_cluster.append(bucket_fps[j].id)

                if len(current_cluster) > 1:
                    clusters.append(DuplicateCluster(ids=current_cluster))
                    processed_ids.update(current_cluster)

        return clusters

class DualThresholdComparer(BaseComparer):
    """快手论文后训练阶段的双重阈值比较器（图像0.98 OR 文本0.50）"""
    def __init__(self, image_threshold: float = 0.98, text_threshold: float = 0.50):
        self.image_threshold = image_threshold
        self.text_threshold = text_threshold

    def find_duplicates(self, fingerprints: List[Fingerprint]) -> List[DuplicateCluster]:
        valid_fps = [fp for fp in fingerprints if fp.value and isinstance(fp.value, dict)
                    and 'image' in fp.value and 'text' in fp.value]
        if not valid_fps:
            return []

        clusters = []
        processed_ids = set()

        for i in tqdm(range(len(valid_fps)), desc="Computing dual threshold similarity"):
            if valid_fps[i].id in processed_ids:
                continue

            current_cluster = [valid_fps[i].id]
            fp1 = valid_fps[i]

            for j in range(i + 1, len(valid_fps)):
                if valid_fps[j].id in processed_ids:
                    continue

                fp2 = valid_fps[j]

                # 计算图像相似度
                fp1_dict = fp1.value if isinstance(fp1.value, dict) else {}
                fp2_dict = fp2.value if isinstance(fp2.value, dict) else {}

                img_vec1 = np.array(fp1_dict.get('image', []), dtype=np.float32)
                img_vec2 = np.array(fp2_dict.get('image', []), dtype=np.float32)
                img_similarity = np.dot(img_vec1, img_vec2) / (np.linalg.norm(img_vec1) * np.linalg.norm(img_vec2))

                # 计算文本相似度
                text_vec1 = np.array(fp1_dict.get('text', []), dtype=np.float32)
                text_vec2 = np.array(fp2_dict.get('text', []), dtype=np.float32)
                text_similarity = np.dot(text_vec1, text_vec2) / (np.linalg.norm(text_vec1) * np.linalg.norm(text_vec2))

                # 快手论文：满足任一阈值就认为重复（OR关系）
                if img_similarity > self.image_threshold or text_similarity > self.text_threshold:
                    current_cluster.append(fp2.id)

            if len(current_cluster) > 1:
                clusters.append(DuplicateCluster(ids=current_cluster))
                processed_ids.update(current_cluster)

        return clusters

COMPARER_REGISTRY = {
    "hash_map": HashMapComparer,
    "hamming_distance": HammingDistanceComparer,
    "faiss_index": FAISSComparer,
    "jaccard_similarity": JaccardSimilarityComparer,
    "dual_threshold": DualThresholdComparer,
}