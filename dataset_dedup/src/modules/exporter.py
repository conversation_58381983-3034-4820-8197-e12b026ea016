import json
import os
from typing import List, Dict
from ..core.data_model import DataUnit
from .base import BaseExporter

class RebuildExporter(BaseExporter):
    def export(self, survivor_ids: List[str], output_path: str, id_to_data_unit: Dict[str, DataUnit]):
        print(f"INFO: [Exporter] Exporting {len(survivor_ids)} cleaned data items to {output_path}...")
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        cleaned_data = [id_to_data_unit[id].content for id in survivor_ids]
        
        with open(output_path, 'w', encoding='utf-8') as f_out:
            json.dump(cleaned_data, f_out, indent=4, ensure_ascii=False)

        print("INFO: [Exporter] Export complete.")

EXPORTER_REGISTRY = {
    "rebuild_dataset": RebuildExporter,
}