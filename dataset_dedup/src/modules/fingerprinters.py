import hashlib
from PIL import Image
import imagehash
import torch
from transformers import CLIPProcessor, CLIPModel
from typing import Optional

from .base import BaseFingerprinter
from ..core.data_model import DataUnit, Fingerprint
from ..utils import get_value_by_path

class FileContentHashFingerprinter(BaseFingerprinter):
    def fingerprint(self, data_unit: DataUnit, **kwargs) -> Fingerprint:
        image_path = get_value_by_path(data_unit.content, kwargs.get("image", ""))
        if not image_path:
            print(f"WARNING: image_path {image_path} doesn't exist, please check your YAML config file.")
            return Fingerprint(id=data_unit.id, value=None)
        try:
            with open(image_path, 'rb') as f:
                return Fingerprint(id=data_unit.id, value=hashlib.sha256(f.read()).hexdigest())
        except FileNotFoundError: return Fingerprint(id=data_unit.id, value=None)

class PerceptualHashFingerprinter(BaseFingerprinter):
    def fingerprint(self, data_unit: DataUnit, **kwargs) -> Fingerprint:
        image_path = get_value_by_path(data_unit.content, kwargs.get("image", ""))
        if not image_path:
            print(f"WARNING: image_path {image_path} doesn't exist, please check your YAML config file.")
            return Fingerprint(id=data_unit.id, value=None)
        try:
            with Image.open(image_path) as img:
                return Fingerprint(id=data_unit.id, value=str(imagehash.phash(img)))
        except (FileNotFoundError, Exception): return Fingerprint(id=data_unit.id, value=None)

class ImageEmbedder(BaseFingerprinter):
    _instance = None
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(ImageEmbedder, cls).__new__(cls)
            cls._instance.initialized = False
        return cls._instance
    
    def __init__(self, model_name: str = "openai/clip-vit-base-patch32", device: Optional[str] = None):
        if self.initialized: return
        self.device = device if device else ("cuda" if torch.cuda.is_available() else "cpu")
        print(f"INFO: Initializing ImageEmbedder with model '{model_name}' on device '{self.device}'...")
        self.model = CLIPModel.from_pretrained(model_name).to(self.device)
        self.processor = CLIPProcessor.from_pretrained(model_name, use_fast=False)
        self.model.eval()
        self.initialized = True
        print("INFO: CLIP model loaded.")

    def fingerprint(self, data_unit: DataUnit, **kwargs) -> Fingerprint:
        image_path = get_value_by_path(data_unit.content, kwargs.get("image"))
        if not image_path:
            print(f"WARNING: image_path {image_path} doesn't exist, please check your YAML config file.")
            return Fingerprint(id=data_unit.id, value=None)
        try:
            with Image.open(image_path) as img:
                img = img.convert("RGB")
                inputs = self.processor(images=img, return_tensors="pt").to(self.device)
                with torch.no_grad():
                    features = self.model.get_image_features(**inputs)
                features /= features.norm(dim=-1, keepdim=True)
                return Fingerprint(id=data_unit.id, value=features.cpu().numpy().flatten().tolist())
        except (FileNotFoundError, Exception): return Fingerprint(id=data_unit.id, value=None)



FINGERPRINTER_REGISTRY = {
    "file_content_hash": FileContentHashFingerprinter,
    "perceptual_hash": PerceptualHashFingerprinter,
    "image_embedder": ImageEmbedder,
}