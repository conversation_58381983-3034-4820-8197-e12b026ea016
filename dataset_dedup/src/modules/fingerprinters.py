import hashlib
from PIL import Image
import imagehash
import torch
from transformers import CLIPProcessor, CLIPModel
from typing import Optional
import mmh3

from .base import BaseFingerprinter
from ..core.data_model import DataUnit, Fingerprint
from ..utils import get_value_by_path

class FileContentHashFingerprinter(BaseFingerprinter):
    def fingerprint(self, data_unit: DataUnit, **kwargs) -> Fingerprint:
        image_path = get_value_by_path(data_unit.content, kwargs.get("image", ""))
        if not image_path:
            print(f"WARNING: image_path {image_path} doesn't exist, please check your YAML config file.")
            return Fingerprint(id=data_unit.id, value=None)
        try:
            with open(image_path, 'rb') as f:
                return Fingerprint(id=data_unit.id, value=hashlib.sha256(f.read()).hexdigest())
        except FileNotFoundError: return Fingerprint(id=data_unit.id, value=None)

class PHashFingerprinter(BaseFingerprinter):
    def fingerprint(self, data_unit: DataUnit, **kwargs) -> Fingerprint:
        image_path = get_value_by_path(data_unit.content, kwargs.get("image", ""))
        if not image_path:
            print(f"WARNING: image_path {image_path} doesn't exist, please check your YAML config file.")
            return Fingerprint(id=data_unit.id, value=None)
        try:
            with Image.open(image_path) as img:
                return Fingerprint(id=data_unit.id, value=str(imagehash.phash(img)))
        except (FileNotFoundError, Exception): return Fingerprint(id=data_unit.id, value=None)

class ImageEmbedder(BaseFingerprinter):
    _instance = None
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(ImageEmbedder, cls).__new__(cls)
            cls._instance.initialized = False
        return cls._instance
    
    def __init__(self, model_name: str = "openai/clip-vit-base-patch32", device: Optional[str] = None):
        if self.initialized: return
        self.device = device if device else ("cuda" if torch.cuda.is_available() else "cpu")
        print(f"INFO: Initializing ImageEmbedder with model '{model_name}' on device '{self.device}'...")
        self.model = CLIPModel.from_pretrained(model_name).to(self.device)
        self.processor = CLIPProcessor.from_pretrained(model_name, use_fast=False)
        self.model.eval()
        self.initialized = True
        print("INFO: CLIP model loaded.")

    def fingerprint(self, data_unit: DataUnit, **kwargs) -> Fingerprint:
        image_path = get_value_by_path(data_unit.content, kwargs.get("image"))
        if not image_path:
            print(f"WARNING: image_path {image_path} doesn't exist, please check your YAML config file.")
            return Fingerprint(id=data_unit.id, value=None)
        try:
            with Image.open(image_path) as img:
                img = img.convert("RGB")
                inputs = self.processor(images=img, return_tensors="pt").to(self.device)
                with torch.no_grad():
                    features = self.model.get_image_features(**inputs)
                features /= features.norm(dim=-1, keepdim=True)
                return Fingerprint(id=data_unit.id, value=features.cpu().numpy().flatten().tolist())
        except (FileNotFoundError, Exception): return Fingerprint(id=data_unit.id, value=None)



class PHashMinHashFingerprinter(BaseFingerprinter):
    """快手论文中的PHash+MinHash去重方法实现"""
    def __init__(self, num_perm: int = 128):
        self.num_perm = num_perm

    def fingerprint(self, data_unit: DataUnit, **kwargs) -> Fingerprint:
        image_path = get_value_by_path(data_unit.content, kwargs.get("image", ""))
        if not image_path:
            print(f"WARNING: image_path {image_path} doesn't exist, please check your YAML config file.")
            return Fingerprint(id=data_unit.id, value=None)
        try:
            with Image.open(image_path) as img:
                # 1. 计算pHash得到64位二进制字符串
                phash = imagehash.phash(img, hash_size=8)  # 8x8 = 64位
                phash_binary = format(int(str(phash), 16), '064b')

                # 2. 提取1的位置ID列表
                ones_positions = [i for i, bit in enumerate(phash_binary) if bit == '1']

                # 3. 使用128位排列哈希构建minHash
                min_hashes = []
                for i in range(self.num_perm):
                    if ones_positions:
                        # 对每个1的位置进行哈希，取最小值
                        hashes = [mmh3.hash(str(pos), seed=i) for pos in ones_positions]
                    else:
                        min_hashes.append(0)

                return Fingerprint(id=data_unit.id, value=min_hashes)
        except (FileNotFoundError, Exception) as e:
            print(f"ERROR: Failed to process {image_path}: {e}")
            return Fingerprint(id=data_unit.id, value=None)

class DualCLIPFingerprinter(BaseFingerprinter):
    """快手论文后训练阶段的双重CLIP编码器（图像+文本）"""
    _instance = None
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(DualCLIPFingerprinter, cls).__new__(cls)
            cls._instance.initialized = False
        return cls._instance

    def __init__(self, model_name: str = "openai/clip-vit-base-patch32", device: Optional[str] = None):
        if self.initialized: return
        self.device = device if device else ("cuda" if torch.cuda.is_available() else "cpu")
        print(f"INFO: Initializing DualCLIPFingerprinter with model '{model_name}' on device '{self.device}'...")
        self.model = CLIPModel.from_pretrained(model_name).to(self.device)
        self.processor = CLIPProcessor.from_pretrained(model_name, use_fast=False)
        self.model.eval()
        self.initialized = True
        print("INFO: Dual CLIP model loaded.")

    def fingerprint(self, data_unit: DataUnit, **kwargs) -> Fingerprint:
        image_path = get_value_by_path(data_unit.content, kwargs.get("image", ""))
        text_path = kwargs.get("text", "messages[1].content")  # 默认获取assistant的回复
        text_content = get_value_by_path(data_unit.content, text_path)

        if not image_path or not text_content:
            print(f"WARNING: Missing image_path {image_path} or text_content, please check your YAML config file.")
            return Fingerprint(id=data_unit.id, value=None)

        try:
            with Image.open(image_path) as img:
                img = img.convert("RGB")

                # 同时处理图像和文本
                inputs = self.processor(
                    images=img,
                    text=text_content,
                    return_tensors="pt",
                    padding=True,
                    truncation=True
                ).to(self.device)

                with torch.no_grad():
                    # 获取图像和文本特征
                    image_features = self.model.get_image_features(pixel_values=inputs['pixel_values'])
                    text_features = self.model.get_text_features(input_ids=inputs['input_ids'], attention_mask=inputs['attention_mask'])

                    # 归一化
                    image_features /= image_features.norm(dim=-1, keepdim=True)
                    text_features /= text_features.norm(dim=-1, keepdim=True)

                    # 返回组合特征：[image_features, text_features]
                    combined_features = {
                        'image': image_features.cpu().numpy().flatten().tolist(),
                        'text': text_features.cpu().numpy().flatten().tolist()
                    }

                return Fingerprint(id=data_unit.id, value=combined_features)
        except (FileNotFoundError, Exception) as e:
            print(f"ERROR: Failed to process {image_path} or text: {e}")
            return Fingerprint(id=data_unit.id, value=None)

FINGERPRINTER_REGISTRY = {
    "file_content_hash": FileContentHashFingerprinter,
    "phash": PHashFingerprinter,
    "image_embedder": ImageEmbedder,
    "phash_minhash": PHashMinHashFingerprinter,
    "dual_clip": DualCLIPFingerprinter,
}