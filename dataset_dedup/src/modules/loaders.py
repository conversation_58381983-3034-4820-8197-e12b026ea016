import json
from typing import List
from ..core.data_model import DataUnit
from .base import BaseLoader

class JsonListLoader(BaseLoader):
    """加载一个包含JSON对象列表的JSON文件。"""
    def load(self, path: str) -> List[DataUnit]:
        print(f"INFO: [Loader] Loading data from {path}...")
        data_units = []
        with open(path, 'r', encoding='utf-8') as f:
            data_list = json.load(f)
            for i, item in enumerate(data_list):
                unit_id = f"{path}:item:{i}"
                data_units.append(DataUnit(id=unit_id, content=item))
        print(f"INFO: [Loader] Loaded {len(data_units)} data units.")
        return data_units

LOADER_REGISTRY = {
    "json_list": JsonListLoader,
}