from typing import List, Dict, Set
from ..core.data_model import DuplicateCluster, DataUnit
from .base import BaseStrategy
from ..utils import get_value_by_path

class KeepFirstStrategy(BaseStrategy):
    def apply(self, clusters: List[DuplicateCluster], id_to_data_unit: Dict[str, DataUnit], current_survivor_list: List[str], **kwargs) -> List[str]:
        ids_to_remove: Set[str] = set()
        for cluster in clusters:
            ids_to_remove.update(cluster.ids[1:])
        return [id for id in current_survivor_list if id not in ids_to_remove]

class KeepLongestStrategy(BaseStrategy):
    def __init__(self, field: str):
        self.field = field

    def apply(self, clusters: List[DuplicateCluster], id_to_data_unit: Dict[str, DataUnit], current_survivor_list: List[str], **kwargs) -> List[str]:
        ids_to_remove: Set[str] = set()
        for cluster in clusters:
            longest_id = ""
            max_len = -1
            for id in cluster.ids:
                content = get_value_by_path(id_to_data_unit[id].content, self.field)
                if isinstance(content, str) and len(content) > max_len:
                    max_len = len(content)
                    longest_id = id
            
            ids_to_remove.update(id for id in cluster.ids if id != longest_id)
        
        return [id for id in current_survivor_list if id not in ids_to_remove]

STRATEGY_REGISTRY = {
    "keep_first": KeepFirstStrategy,
    "keep_longest": KeepLongestStrategy,
}