from typing import Dict, Any, Type, Set
from .core.data_model import <PERSON>Uni<PERSON>, Fingerprint, DuplicateCluster
from .modules.base import <PERSON><PERSON><PERSON><PERSON>, BaseFingerprinter, BaseComparer, BaseStrategy, BaseExporter
from .modules.loaders import LOADER_REGISTRY
from .modules.fingerprinters import FINGERPRINTER_REGISTRY
from .modules.comparers import COMPARER_REGISTRY
from .modules.strategies import STRATEGY_REGISTRY
from .modules.exporter import EXPORTER_REGISTRY

class PipelineExecutor:
    def __init__(self, config: Dict[str, Any]):
        self.config = config

    def _get_plugin_instance(self, registry: Dict[str, Type], config_block: Dict[str, Any]):
        plugin_name = config_block.get('name')
        if not plugin_name: raise ValueError(f"Plugin configuration block is missing 'name' key: {config_block}")
        plugin_class = registry.get(plugin_name)
        if not plugin_class: raise ValueError(f"Unknown plugin name '{plugin_name}'. Available: {list(registry.keys())}")
        plugin_params = config_block.get('params', {})
        return plugin_class(**plugin_params)

    def run(self):
        print("\n--- Deduplication Pipeline Started ---")
        print("\n[阶段 1/3] Loading data...")
        loader: BaseLoader = self._get_plugin_instance(LOADER_REGISTRY, self.config['data_source'])
        data_units = loader.load(self.config['data_source']['path'])
        id_to_data_unit: Dict[str, DataUnit] = {du.id: du for du in data_units}
        survivor_ids: Set[str] = set(id_to_data_unit.keys())
        print(f"INFO: Loaded {len(data_units)} total data units.")

        print("\n[阶段 2/3] Executing deduplication pipeline...")
        for i, stage_config in enumerate(self.config['pipeline']):
            stage_name = stage_config.get('stage', f'Stage {i+1}')
            print(f"\n--- Running {stage_name} ---")
            if not survivor_ids:
                print("INFO: No data left to process. Skipping.")
                break
            
            current_survivor_list = sorted(list(survivor_ids))
            current_data_units = [id_to_data_unit[id] for id in current_survivor_list]
            
            fingerprint_target = self.config.get('fingerprint_target', {})
            fingerprinter: BaseFingerprinter = self._get_plugin_instance(FINGERPRINTER_REGISTRY, stage_config['fingerprinter'])
            fingerprints = [fingerprinter.fingerprint(du, **fingerprint_target) for du in current_data_units]

            comparer: BaseComparer = self._get_plugin_instance(COMPARER_REGISTRY, stage_config['comparer'])
            clusters = comparer.find_duplicates(fingerprints)

            strategy: BaseStrategy = self._get_plugin_instance(STRATEGY_REGISTRY, stage_config['strategy'])
            survivor_ids_for_stage = strategy.apply(clusters, id_to_data_unit, current_survivor_list)
            
            survivor_ids = set(survivor_ids_for_stage)
            print(f"INFO: {len(survivor_ids)} items survived this stage.")

        print("\n[阶段 3/3] Exporting results...")
        exporter_config = self.config.get('exporter', {'name': 'rebuild_dataset'})
        exporter: BaseExporter = self._get_plugin_instance(EXPORTER_REGISTRY, exporter_config)
        
        final_survivor_ids = [id for id in id_to_data_unit if id in survivor_ids]
        exporter.export(
            survivor_ids=final_survivor_ids,
            output_path=self.config['output_path'],
            id_to_data_unit=id_to_data_unit
        )
        print("\n--- Deduplication Pipeline Finished Successfully ---")