import re
from typing import Any, Dict

def get_value_by_path(data: Dict, path: str) -> Any:
    """
    根据一个点和方括号表示的路径字符串，从嵌套字典中获取值。
    例如: "messages[1].content"
    """
    if not path:
        return None
    
    # 将 "images[0]" 这种路径分割成 ['images', '0']
    keys = re.split(r'\.|\[|\]', path)
    keys = [k for k in keys if k]  # 移除分割产生的空字符串
    
    current_val = data
    for key in keys:
        if isinstance(current_val, list) and key.isdigit():
            try:
                current_val = current_val[int(key)]
            except IndexError:
                return None # 索引越界
        elif isinstance(current_val, dict) and key in current_val:
            current_val = current_val[key]
        else:
            return None # 键不存在或类型不匹配
    return current_val